import os
import json
import threading
import time
import shutil
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from datetime import datetime

# Import database and authentication modules
from database import db_manager
from auth import require_auth, get_current_user, session_manager, LoginWindow
from logging_config import user_activity_logger, system_event_logger, log_error, log_info

TASKS_FILE = "backup_tasks.json"  # Legacy file - will migrate to database

# Check authentication before starting the application
def check_authentication():
    """Check if user is authenticated, show login if not"""
    if not session_manager.is_authenticated():
        login_window = LoginWindow(session_manager)
        if not login_window.show_login():
            import sys
            sys.exit(0)  # Exit if login cancelled

    # Log application start
    current_user = get_current_user()
    user_activity_logger.log_user_action("backup_app_started",
                                        f"User opened backup scheduler app")
    system_event_logger.log_startup()

class BackupTask:
    def __init__(self, name, source, destination, interval, status="Stopped", task_id=None, user_id=None):
        self.task_id = task_id
        self.user_id = user_id
        self.name = name
        self.source = source
        self.destination = destination
        self.interval = interval
        self.status = status
        self.thread = None
        self.running = False

    def to_dict(self):
        return {
            "name": self.name,
            "source": self.source,
            "destination": self.destination,
            "interval": self.interval,
            "status": self.status
        }

    def start(self):
        if not self.running:
            self.running = True
            self.status = "Running"
            # Update status in database
            if self.task_id:
                db_manager.update_backup_task_status(self.task_id, "Running")

            system_event_logger.log_backup_event("task_started", self.name)
            self.thread = threading.Thread(target=self.run_backup, daemon=True)
            self.thread.start()

    def stop(self):
        self.running = False
        self.status = "Stopped"
        # Update status in database
        if self.task_id:
            db_manager.update_backup_task_status(self.task_id, "Stopped")

        system_event_logger.log_backup_event("task_stopped", self.name)

    def run_backup(self):
        while self.running:
            self.perform_backup()
            time.sleep(self.interval)

    def perform_backup(self):
        for root, dirs, files in os.walk(self.source):
            rel_path = os.path.relpath(root, self.source)
            dest_path = os.path.join(self.destination, rel_path)
            os.makedirs(dest_path, exist_ok=True)

            for file in files:
                src_file = os.path.join(root, file)
                dest_file = os.path.join(dest_path, file)
                if not os.path.exists(dest_file) or os.path.getmtime(src_file) > os.path.getmtime(dest_file):
                    shutil.copy2(src_file, dest_file)

class BackupSchedulerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Backup Scheduler")

        # Get current user
        self.current_user = get_current_user()

        # Add user info and logout button
        user_frame = ttk.Frame(root)
        user_frame.pack(fill=tk.X, padx=10, pady=5)

        user_info_label = ttk.Label(user_frame, text=f"Logged in as: {self.current_user['username']}")
        user_info_label.pack(side=tk.LEFT)

        def logout_and_exit():
            """Logout user and exit application"""
            user_activity_logger.log_user_action("logout", "User logged out from backup scheduler")
            session_manager.logout()
            root.destroy()

        logout_btn = ttk.Button(user_frame, text="Logout", command=logout_and_exit)
        logout_btn.pack(side=tk.RIGHT)

        self.tasks = []
        self.load_tasks()

        self.tree = ttk.Treeview(root, columns=("Name", "Source", "Destination", "Interval", "Status"), show='headings')
        for col in ("Name", "Source", "Destination", "Interval", "Status"):
            self.tree.heading(col, text=col)
        self.tree.pack(fill=tk.BOTH, expand=True)

        btn_frame = tk.Frame(root)
        btn_frame.pack(pady=10)

        tk.Button(btn_frame, text="Add Task", command=self.add_task).grid(row=0, column=0, padx=5)
        tk.Button(btn_frame, text="Edit Task", command=self.edit_task).grid(row=0, column=1, padx=5)
        tk.Button(btn_frame, text="Delete Task", command=self.delete_task).grid(row=0, column=2, padx=5)
        tk.Button(btn_frame, text="Start Task", command=self.start_task).grid(row=0, column=3, padx=5)
        tk.Button(btn_frame, text="Stop Task", command=self.stop_task).grid(row=0, column=4, padx=5)

        self.refresh_tree()

    def add_task(self):
        self.task_form()

    def edit_task(self):
        selected = self.tree.selection()
        if selected:
            index = self.tree.index(selected[0])
            task = self.tasks[index]
            self.task_form(task, index)

    def delete_task(self):
        selected = self.tree.selection()
        if selected:
            index = self.tree.index(selected[0])
            task = self.tasks[index]

            # Confirm deletion
            if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete task '{task.name}'?"):
                try:
                    task.stop()  # Stop the task first

                    # Delete from database
                    if task.task_id and db_manager.delete_backup_task(task.task_id, self.current_user['id']):
                        user_activity_logger.log_user_action("backup_task_deleted", f"Deleted backup task: {task.name}")

                        # Remove from local list
                        del self.tasks[index]
                        self.refresh_tree()
                        messagebox.showinfo("Success", "Backup task deleted successfully")
                    else:
                        messagebox.showerror("Error", "Failed to delete backup task from database")

                except Exception as e:
                    log_error(e, "Error deleting backup task")
                    messagebox.showerror("Error", f"Failed to delete task: {e}")

    def start_task(self):
        selected = self.tree.selection()
        if selected:
            index = self.tree.index(selected[0])
            task = self.tasks[index]
            task.start()
            self.refresh_tree()

    def stop_task(self):
        selected = self.tree.selection()
        if selected:
            index = self.tree.index(selected[0])
            task = self.tasks[index]
            task.stop()
            self.refresh_tree()

    def refresh_tree(self):
        for row in self.tree.get_children():
            self.tree.delete(row)
        for task in self.tasks:
            self.tree.insert('', tk.END, values=(task.name, task.source, task.destination, f"{task.interval}s", task.status))

    def task_form(self, task=None, index=None):
        form = tk.Toplevel(self.root)
        form.title("Task Form")

        tk.Label(form, text="Task Name").grid(row=0, column=0)
        name_entry = tk.Entry(form)
        name_entry.grid(row=0, column=1)

        tk.Label(form, text="Source Folder").grid(row=1, column=0)
        source_entry = tk.Entry(form)
        source_entry.grid(row=1, column=1)
        tk.Button(form, text="Browse", command=lambda: source_entry.insert(0, filedialog.askdirectory())).grid(row=1, column=2)

        tk.Label(form, text="Destination Folder").grid(row=2, column=0)
        dest_entry = tk.Entry(form)
        dest_entry.grid(row=2, column=1)
        tk.Button(form, text="Browse", command=lambda: dest_entry.insert(0, filedialog.askdirectory())).grid(row=2, column=2)

        tk.Label(form, text="Interval (s)").grid(row=3, column=0)
        interval_entry = tk.Entry(form)
        interval_entry.grid(row=3, column=1)

        if task:
            name_entry.insert(0, task.name)
            source_entry.insert(0, task.source)
            dest_entry.insert(0, task.destination)
            interval_entry.insert(0, str(task.interval))

        def save():
            name = name_entry.get().strip()
            source = source_entry.get().strip()
            destination = dest_entry.get().strip()

            if not name or not source or not destination:
                messagebox.showerror("Error", "All fields are required")
                return

            try:
                interval = int(interval_entry.get())
                if interval <= 0:
                    messagebox.showerror("Error", "Interval must be a positive integer")
                    return

                if task:
                    # Editing existing task - not implemented for database yet
                    messagebox.showinfo("Info", "Task editing not yet implemented with database")
                else:
                    # Adding new task
                    if db_manager.save_backup_task(self.current_user['id'], name, source, destination, interval):
                        user_activity_logger.log_user_action("backup_task_created", f"Created backup task: {name}")

                        # Reload tasks from database
                        self.tasks.clear()
                        self.load_tasks()
                        self.refresh_tree()
                        form.destroy()
                        messagebox.showinfo("Success", "Backup task created successfully")
                    else:
                        messagebox.showerror("Error", "Failed to save backup task")

            except ValueError:
                messagebox.showerror("Error", "Interval must be an integer")
            except Exception as e:
                log_error(e, "Error saving backup task")
                messagebox.showerror("Error", f"Failed to save task: {e}")

        tk.Button(form, text="Save", command=save).grid(row=4, column=0, columnspan=2)

    def load_tasks(self):
        """Load backup tasks from database"""
        try:
            # Load from database
            db_tasks = db_manager.get_backup_tasks(self.current_user['id'])

            for db_task in db_tasks:
                task = BackupTask(
                    name=db_task['name'],
                    source=db_task['source_path'],
                    destination=db_task['destination_path'],
                    interval=db_task['interval_seconds'],
                    status=db_task['status'],
                    task_id=db_task['id'],
                    user_id=db_task['user_id']
                )
                self.tasks.append(task)

        except Exception as e:
            log_error(e, "Error loading backup tasks")
            messagebox.showerror("Error", f"Could not load backup tasks: {e}")

    def save_tasks(self):
        """Save backup tasks to database - called after adding/editing tasks"""
        # This method is now handled by individual task operations
        pass

if __name__ == "__main__":
    # Check authentication before starting the application
    check_authentication()

    root = tk.Tk()
    app = BackupSchedulerApp(root)
    root.mainloop()
