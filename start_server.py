#!/usr/bin/env python3
"""
Startup script for the Folder Management System server
"""
import os
import sys
import argparse
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        ('flask', 'flask'),
        ('flask-login', 'flask_login'),
        ('pillow', 'PIL'),
        ('bcrypt', 'bcrypt'),
        ('requests', 'requests'),
        ('sqlalchemy', 'sqlalchemy')
    ]

    missing_packages = []

    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)
    
    if missing_packages:
        print("Missing required packages:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nInstall missing packages with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def setup_environment():
    """Setup environment variables and directories"""
    # Create necessary directories
    directories = ['logs', 'backups']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"Created directory: {directory}")
    
    # Set default environment variables if not set
    env_defaults = {
        'FLASK_ENV': 'development',
        'SECRET_KEY': 'dev-secret-key-change-in-production',
        'DATABASE_PATH': 'app_database.db',
        'LOG_LEVEL': 'INFO'
    }
    
    for key, value in env_defaults.items():
        if key not in os.environ:
            os.environ[key] = value
            print(f"Set environment variable: {key}={value}")

def initialize_database():
    """Initialize the database"""
    try:
        from database import db_manager
        print("Database initialized successfully")
        return True
    except Exception as e:
        print(f"Error initializing database: {e}")
        return False

def start_server(host='0.0.0.0', port=5000, debug=False):
    """Start the Flask server"""
    try:
        from server import app
        print(f"Starting server on {host}:{port}")
        print(f"Debug mode: {debug}")
        print(f"Access the application at: http://localhost:{port}")
        print("Press Ctrl+C to stop the server")
        
        app.run(host=host, port=port, debug=debug, threaded=True)
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Error starting server: {e}")
        return False
    
    return True

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Start the Folder Management System server')
    parser.add_argument('--host', default='0.0.0.0', help='Host to bind to (default: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5000, help='Port to bind to (default: 5000)')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--no-check', action='store_true', help='Skip dependency checks')
    parser.add_argument('--setup-only', action='store_true', help='Only setup environment, don\'t start server')
    
    args = parser.parse_args()
    
    print("=" * 50)
    print("Folder Management System Server")
    print("=" * 50)
    
    # Check dependencies
    if not args.no_check:
        print("Checking dependencies...")
        if not check_dependencies():
            sys.exit(1)
        print("All dependencies are installed ✓")
    
    # Setup environment
    print("Setting up environment...")
    setup_environment()
    print("Environment setup complete ✓")
    
    # Initialize database
    print("Initializing database...")
    if not initialize_database():
        sys.exit(1)
    print("Database initialization complete ✓")
    
    if args.setup_only:
        print("Setup complete. Use --no-setup to start the server.")
        return
    
    # Start server
    print("Starting server...")
    start_server(args.host, args.port, args.debug)

if __name__ == '__main__':
    main()
