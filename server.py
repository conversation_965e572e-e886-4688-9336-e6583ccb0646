"""
Flask server for network access to the folder management system
"""
from flask import Flask, request, jsonify, session, render_template_string
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, UserMixin, login_user, logout_user, login_required, current_user
import os
import json
from datetime import datetime, timedelta
from database import db_manager
from logging_config import system_event_logger, user_activity_logger, log_error, log_info

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', 'your-secret-key-change-this-in-production')
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=24)

# Flask-Login setup
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

class User(UserMixin):
    def __init__(self, user_data):
        self.id = str(user_data['id'])
        self.username = user_data['username']
        self.email = user_data['email']
        self.role = user_data['role']
        self.is_active_user = user_data['is_active']

@login_manager.user_loader
def load_user(user_id):
    user_data = db_manager.get_user_by_id(int(user_id))
    if user_data and user_data['is_active']:
        return User(user_data)
    return None

# HTML Templates (simple inline templates for basic UI)
LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>Folder Management System - Login</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 400px; margin: 100px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input[type="text"], input[type="password"] { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .error { color: red; margin-top: 10px; }
        .success { color: green; margin-top: 10px; }
    </style>
</head>
<body>
    <h2>Folder Management System</h2>
    <h3>Login</h3>
    <form method="POST">
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" required>
        </div>
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" required>
        </div>
        <button type="submit">Login</button>
    </form>
    {% if success %}
        <div class="success">{{ success }}</div>
    {% endif %}
    {% if error %}
        <div class="error">{{ error }}</div>
    {% endif %}
    <p><a href="/register">Register new account</a></p>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>Folder Management System - Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; }
        .card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin-bottom: 20px; }
        .btn { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn:hover { background-color: #0056b3; }
        .btn-danger { background-color: #dc3545; }
        .btn-danger:hover { background-color: #c82333; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Folder Management System</h1>
        <div>
            <span>Welcome, {{ current_user.username }} ({{ current_user.role }})</span>
            <a href="/logout" class="btn btn-danger">Logout</a>
        </div>
    </div>
    
    <div class="grid">
        <div class="card">
            <h3>Folder Structures</h3>
            <p>Manage your folder structures</p>
            <a href="/api/folder-structures" class="btn">View Structures</a>
        </div>
        
        <div class="card">
            <h3>Backup Tasks</h3>
            <p>Manage your backup tasks</p>
            <a href="/api/backup-tasks" class="btn">View Tasks</a>
        </div>
        
        <div class="card">
            <h3>Activity Logs</h3>
            <p>View system activity</p>
            <a href="/api/activity-logs" class="btn">View Logs</a>
        </div>
        
        {% if current_user.role == 'admin' %}
        <div class="card">
            <h3>User Management</h3>
            <p>Manage system users</p>
            <a href="/api/users" class="btn">Manage Users</a>
        </div>
        {% endif %}
    </div>
    
    <div class="card">
        <h3>API Endpoints</h3>
        <p>Use these endpoints for programmatic access:</p>
        <ul>
            <li><strong>GET /api/folder-structures</strong> - Get your folder structures</li>
            <li><strong>POST /api/folder-structures</strong> - Create new folder structure</li>
            <li><strong>GET /api/backup-tasks</strong> - Get your backup tasks</li>
            <li><strong>POST /api/backup-tasks</strong> - Create new backup task</li>
            <li><strong>GET /api/activity-logs</strong> - Get activity logs</li>
        </ul>
    </div>
</body>
</html>
'''

# Routes
@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        # Handle login form submission
        username = request.form.get('username')
        password = request.form.get('password')

        if username and password:
            user_data = db_manager.authenticate_user(username, password)
            if user_data:
                user = User(user_data)
                login_user(user, remember=True)

                # Log login
                system_event_logger.log_network_event("web_login", f"User {username} logged in via web interface")

                return render_template_string(DASHBOARD_TEMPLATE)
            else:
                return render_template_string(LOGIN_TEMPLATE, error="Invalid username or password")
        else:
            return render_template_string(LOGIN_TEMPLATE, error="Please enter both username and password")

    # GET request
    if current_user.is_authenticated:
        return render_template_string(DASHBOARD_TEMPLATE)
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/login', methods=['GET', 'POST'])
def login():
    # If already authenticated, redirect to dashboard
    if current_user.is_authenticated:
        return render_template_string(DASHBOARD_TEMPLATE)

    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')

        if not username or not password:
            return render_template_string(LOGIN_TEMPLATE, error="Please enter both username and password")

        user_data = db_manager.authenticate_user(username, password)
        if user_data:
            user = User(user_data)
            login_user(user, remember=True)

            # Log login
            system_event_logger.log_network_event("web_login", f"User {username} logged in via web interface")

            return render_template_string(DASHBOARD_TEMPLATE)
        else:
            return render_template_string(LOGIN_TEMPLATE, error="Invalid username or password")

    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
@login_required
def logout():
    username = current_user.username
    logout_user()
    system_event_logger.log_network_event("web_logout", f"User {username} logged out from web interface")

    # Create a modified login template with success message
    logout_template = LOGIN_TEMPLATE.replace(
        '{% if error %}',
        '{% if success %}<div class="success">{{ success }}</div>{% endif %}{% if error %}'
    )
    return render_template_string(logout_template, success="Logged out successfully")

# API Routes
@app.route('/api/folder-structures', methods=['GET', 'POST'])
@login_required
def api_folder_structures():
    if request.method == 'GET':
        try:
            structures = db_manager.get_folder_structures(int(current_user.id))
            return jsonify({
                'success': True,
                'data': structures
            })
        except Exception as e:
            log_error(e, "Error getting folder structures via API")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    elif request.method == 'POST':
        try:
            data = request.get_json()
            client_name = data.get('client_name')
            project_name = data.get('project_name')
            structure_data = data.get('structure_data')
            
            if not all([client_name, project_name, structure_data]):
                return jsonify({
                    'success': False,
                    'error': 'Missing required fields'
                }), 400
            
            success = db_manager.save_folder_structure(
                int(current_user.id), 
                client_name, 
                project_name, 
                structure_data
            )
            
            if success:
                user_activity_logger.log_user_action(
                    "api_structure_saved", 
                    f"Saved structure for {client_name}/{project_name} via API",
                    int(current_user.id),
                    current_user.username
                )
                return jsonify({
                    'success': True,
                    'message': 'Folder structure saved successfully'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'Failed to save folder structure'
                }), 500
                
        except Exception as e:
            log_error(e, "Error saving folder structure via API")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

@app.route('/api/backup-tasks', methods=['GET', 'POST'])
@login_required
def api_backup_tasks():
    if request.method == 'GET':
        try:
            tasks = db_manager.get_backup_tasks(int(current_user.id))
            return jsonify({
                'success': True,
                'data': tasks
            })
        except Exception as e:
            log_error(e, "Error getting backup tasks via API")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    elif request.method == 'POST':
        try:
            data = request.get_json()
            name = data.get('name')
            source_path = data.get('source_path')
            destination_path = data.get('destination_path')
            interval_seconds = data.get('interval_seconds')
            
            if not all([name, source_path, destination_path, interval_seconds]):
                return jsonify({
                    'success': False,
                    'error': 'Missing required fields'
                }), 400
            
            success = db_manager.save_backup_task(
                int(current_user.id),
                name,
                source_path,
                destination_path,
                int(interval_seconds)
            )
            
            if success:
                user_activity_logger.log_user_action(
                    "api_backup_task_created",
                    f"Created backup task: {name} via API",
                    int(current_user.id),
                    current_user.username
                )
                return jsonify({
                    'success': True,
                    'message': 'Backup task created successfully'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'Failed to create backup task'
                }), 500
                
        except Exception as e:
            log_error(e, "Error creating backup task via API")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

@app.route('/api/activity-logs')
@login_required
def api_activity_logs():
    try:
        # Admin can see all logs, users see only their own
        if current_user.role == 'admin':
            logs = db_manager.get_activity_logs(limit=200)
        else:
            logs = db_manager.get_activity_logs(int(current_user.id), limit=100)
        
        return jsonify({
            'success': True,
            'data': logs
        })
    except Exception as e:
        log_error(e, "Error getting activity logs via API")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/users')
@login_required
def api_users():
    # Admin only endpoint
    if current_user.role != 'admin':
        return jsonify({
            'success': False,
            'error': 'Access denied'
        }), 403
    
    try:
        # This would need to be implemented in database.py
        return jsonify({
            'success': True,
            'message': 'User management API not yet implemented'
        })
    except Exception as e:
        log_error(e, "Error in user management API")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

if __name__ == '__main__':
    # Log server startup
    system_event_logger.log_network_event("server_started", "Flask server started")
    
    # Run the server
    app.run(
        host='0.0.0.0',  # Listen on all interfaces
        port=5000,
        debug=False,  # Set to False in production
        threaded=True
    )
