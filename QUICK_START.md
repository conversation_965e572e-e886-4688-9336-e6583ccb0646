# Quick Start Guide

## 🚀 Get Started in 3 Steps

### 1. Install Dependencies
```bash
pip install -r Requirment.txt
```

### 2. Start the Server (Optional - for network access)
```bash
python start_server.py
```
The server will be available at: http://localhost:5000

### 3. Launch Desktop Application
```bash
python main.py
```

## 🔐 Default Login Credentials
- **Username**: `admin`
- **Password**: `admin123`

⚠️ **Change the default password after first login!**

## 📱 Access Methods

### Desktop GUI
- Run `python main.py`
- Full-featured tkinter interface
- Works offline

### Web Interface
- Start server: `python start_server.py`
- Open browser: http://localhost:5000
- Access from any device on network

### Network Access
1. Start server: `python start_server.py --host 0.0.0.0`
2. Find your IP: `ip addr show` (Linux/Mac) or `ipconfig` (Windows)
3. Access from other devices: `http://YOUR_IP:5000`

## 🎯 What You Can Do

### Folder Structure Management
- Create new folder structures with templates
- Save and load structures from database
- Update existing folder structures
- User-specific data isolation

### Backup Scheduling
- Create automated backup tasks
- Schedule backups with custom intervals
- Start/stop backup tasks
- Monitor backup status

### User Management (Admin)
- Create new user accounts
- Manage user permissions
- View activity logs
- Role-based access control

### Logging & Monitoring
- Comprehensive activity logging
- Error tracking
- System event monitoring
- User action auditing

## 🔧 Configuration

### Environment Variables
```bash
export DATABASE_PATH=app_database.db
export SERVER_HOST=0.0.0.0
export SERVER_PORT=5000
export SECRET_KEY=your-secret-key
export LOG_LEVEL=INFO
```

### Server Options
```bash
# Development mode
python start_server.py --debug

# Custom port
python start_server.py --port 8080

# Setup only (no server start)
python start_server.py --setup-only
```

## 📊 API Usage

### Authentication
Login via web interface to get session cookies.

### Create Folder Structure
```bash
curl -X POST http://localhost:5000/api/folder-structures \
  -H "Content-Type: application/json" \
  -d '{
    "client_name": "My Client",
    "project_name": "My Project",
    "structure_data": [
      {"name": "src", "children": []},
      {"name": "docs", "children": []}
    ]
  }'
```

### Create Backup Task
```bash
curl -X POST http://localhost:5000/api/backup-tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Daily Backup",
    "source_path": "/path/to/source",
    "destination_path": "/path/to/backup",
    "interval_seconds": 86400
  }'
```

## 🗂️ File Structure
```
tkinter-app-/
├── main.py                    # Main launcher
├── folder_structure_creation.py  # Create structures
├── folder_structure_updation.py  # Update structures  
├── backuptool.py             # Backup scheduler
├── server.py                 # Web server
├── database.py               # Database management
├── auth.py                   # Authentication
├── logging_config.py         # Logging system
├── client_api.py             # HTTP client
├── config.py                 # Configuration
├── start_server.py           # Server startup
├── app_database.db           # SQLite database
└── logs/                     # Log files
```

## 🛠️ Troubleshooting

### Database Issues
```bash
# Reset database
rm app_database.db
python start_server.py --setup-only
```

### Port Already in Use
```bash
python start_server.py --port 8080
```

### Permission Errors
```bash
chmod +x start_server.py
```

### Missing Dependencies
```bash
pip install -r Requirment.txt
```

## 📝 Features Added

✅ **Centralized Database** - SQLite with user data isolation  
✅ **User Authentication** - Login/logout with password hashing  
✅ **Network Server** - Flask web server for remote access  
✅ **Comprehensive Logging** - Activity, error, and system logs  
✅ **Multi-user Support** - Role-based permissions  
✅ **Web API** - RESTful endpoints for automation  
✅ **Offline Mode** - Works without network connection  
✅ **Configuration** - Environment-based settings  

## 🎉 You're Ready!

Your folder management system now has:
- **Database storage** instead of JSON files
- **User authentication** and session management
- **Network access** for remote usage
- **Comprehensive logging** for monitoring
- **Multi-user support** with permissions
- **Web API** for automation

Enjoy your enhanced folder management system! 🚀
