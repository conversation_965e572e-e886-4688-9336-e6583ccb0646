"""
Authentication system for the tkinter application
"""
import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from typing import Optional, Dict
from database import db_manager
import logging

logger = logging.getLogger(__name__)

class SessionManager:
    """Manages user sessions"""
    
    def __init__(self):
        self.current_user = None
        self.session_file = "current_session.json"
        self.load_session()
    
    def login(self, username: str, password: str) -> bool:
        """Login user and create session"""
        user = db_manager.authenticate_user(username, password)
        if user:
            self.current_user = user
            self.save_session()
            logger.info(f"User {username} logged in successfully")
            return True
        return False
    
    def logout(self):
        """Logout current user"""
        if self.current_user:
            db_manager.log_activity(self.current_user['id'], "logout", f"User {self.current_user['username']} logged out")
            logger.info(f"User {self.current_user['username']} logged out")
        self.current_user = None
        self.clear_session()
    
    def is_authenticated(self) -> bool:
        """Check if user is authenticated"""
        return self.current_user is not None
    
    def get_current_user(self) -> Optional[Dict]:
        """Get current user data"""
        return self.current_user
    
    def save_session(self):
        """Save session to file"""
        try:
            with open(self.session_file, 'w') as f:
                json.dump(self.current_user, f)
        except Exception as e:
            logger.error(f"Error saving session: {e}")
    
    def load_session(self):
        """Load session from file"""
        try:
            if os.path.exists(self.session_file):
                with open(self.session_file, 'r') as f:
                    self.current_user = json.load(f)
                    # Verify user still exists and is active
                    if self.current_user:
                        user = db_manager.get_user_by_id(self.current_user['id'])
                        if not user or not user['is_active']:
                            self.current_user = None
                            self.clear_session()
        except Exception as e:
            logger.error(f"Error loading session: {e}")
            self.current_user = None
    
    def clear_session(self):
        """Clear session file"""
        try:
            if os.path.exists(self.session_file):
                os.remove(self.session_file)
        except Exception as e:
            logger.error(f"Error clearing session: {e}")

class LoginWindow:
    """Login window for user authentication"""
    
    def __init__(self, session_manager: SessionManager, on_success_callback=None):
        self.session_manager = session_manager
        self.on_success_callback = on_success_callback
        self.root = None
        self.username_var = None
        self.password_var = None
        self.remember_var = None
        
    def show_login(self) -> bool:
        """Show login window and return success status"""
        self.root = tk.Toplevel()
        self.root.title("Login - Folder Management System")
        self.root.geometry("400x300")
        self.root.resizable(False, False)
        
        # Center the window
        self.root.transient()
        self.root.grab_set()
        
        # Variables
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.remember_var = tk.BooleanVar()
        
        self.create_login_ui()
        
        # Wait for window to close
        self.root.wait_window()
        
        return self.session_manager.is_authenticated()
    
    def create_login_ui(self):
        """Create login UI elements"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="Login", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Login form
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill=tk.X, pady=10)
        
        # Username
        ttk.Label(form_frame, text="Username:").grid(row=0, column=0, sticky=tk.W, pady=5)
        username_entry = ttk.Entry(form_frame, textvariable=self.username_var, width=25)
        username_entry.grid(row=0, column=1, pady=5, padx=(10, 0))
        username_entry.focus()
        
        # Password
        ttk.Label(form_frame, text="Password:").grid(row=1, column=0, sticky=tk.W, pady=5)
        password_entry = ttk.Entry(form_frame, textvariable=self.password_var, show="*", width=25)
        password_entry.grid(row=1, column=1, pady=5, padx=(10, 0))
        
        # Remember me checkbox
        remember_check = ttk.Checkbutton(form_frame, text="Remember me", variable=self.remember_var)
        remember_check.grid(row=2, column=1, sticky=tk.W, pady=10, padx=(10, 0))
        
        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=20)
        
        # Login button
        login_btn = ttk.Button(button_frame, text="Login", command=self.handle_login)
        login_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Register button
        register_btn = ttk.Button(button_frame, text="Register", command=self.show_register)
        register_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Cancel button
        cancel_btn = ttk.Button(button_frame, text="Cancel", command=self.handle_cancel)
        cancel_btn.pack(side=tk.RIGHT)
        
        # Bind Enter key to login
        self.root.bind('<Return>', lambda e: self.handle_login())
        
        # Default credentials info
        info_frame = ttk.LabelFrame(main_frame, text="Default Credentials", padding="10")
        info_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Label(info_frame, text="Username: admin", font=("Arial", 9)).pack(anchor=tk.W)
        ttk.Label(info_frame, text="Password: admin123", font=("Arial", 9)).pack(anchor=tk.W)
        ttk.Label(info_frame, text="(Please change default password after first login)", 
                 font=("Arial", 8), foreground="red").pack(anchor=tk.W)
    
    def handle_login(self):
        """Handle login button click"""
        username = self.username_var.get().strip()
        password = self.password_var.get()
        
        if not username or not password:
            messagebox.showerror("Error", "Please enter both username and password")
            return
        
        if self.session_manager.login(username, password):
            messagebox.showinfo("Success", f"Welcome, {username}!")
            if self.on_success_callback:
                self.on_success_callback()
            self.root.destroy()
        else:
            messagebox.showerror("Error", "Invalid username or password")
            self.password_var.set("")  # Clear password field
    
    def handle_cancel(self):
        """Handle cancel button click"""
        self.root.destroy()
    
    def show_register(self):
        """Show registration window"""
        RegisterWindow(self.root).show_register()

class RegisterWindow:
    """Registration window for new users"""
    
    def __init__(self, parent):
        self.parent = parent
        self.root = None
        
    def show_register(self):
        """Show registration window"""
        self.root = tk.Toplevel(self.parent)
        self.root.title("Register - Folder Management System")
        self.root.geometry("450x400")
        self.root.resizable(False, False)
        
        # Center the window
        self.root.transient(self.parent)
        self.root.grab_set()
        
        self.create_register_ui()
    
    def create_register_ui(self):
        """Create registration UI elements"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="Register New User", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Registration form
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill=tk.X, pady=10)
        
        # Variables
        self.username_var = tk.StringVar()
        self.email_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.confirm_password_var = tk.StringVar()
        
        # Username
        ttk.Label(form_frame, text="Username:").grid(row=0, column=0, sticky=tk.W, pady=5)
        username_entry = ttk.Entry(form_frame, textvariable=self.username_var, width=30)
        username_entry.grid(row=0, column=1, pady=5, padx=(10, 0))
        username_entry.focus()
        
        # Email
        ttk.Label(form_frame, text="Email:").grid(row=1, column=0, sticky=tk.W, pady=5)
        email_entry = ttk.Entry(form_frame, textvariable=self.email_var, width=30)
        email_entry.grid(row=1, column=1, pady=5, padx=(10, 0))
        
        # Password
        ttk.Label(form_frame, text="Password:").grid(row=2, column=0, sticky=tk.W, pady=5)
        password_entry = ttk.Entry(form_frame, textvariable=self.password_var, show="*", width=30)
        password_entry.grid(row=2, column=1, pady=5, padx=(10, 0))
        
        # Confirm Password
        ttk.Label(form_frame, text="Confirm Password:").grid(row=3, column=0, sticky=tk.W, pady=5)
        confirm_entry = ttk.Entry(form_frame, textvariable=self.confirm_password_var, show="*", width=30)
        confirm_entry.grid(row=3, column=1, pady=5, padx=(10, 0))
        
        # Password requirements
        req_frame = ttk.LabelFrame(main_frame, text="Password Requirements", padding="10")
        req_frame.pack(fill=tk.X, pady=20)
        
        requirements = [
            "• At least 6 characters long",
            "• Contains letters and numbers",
            "• No spaces allowed"
        ]
        
        for req in requirements:
            ttk.Label(req_frame, text=req, font=("Arial", 9)).pack(anchor=tk.W)
        
        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=20)
        
        # Register button
        register_btn = ttk.Button(button_frame, text="Register", command=self.handle_register)
        register_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Cancel button
        cancel_btn = ttk.Button(button_frame, text="Cancel", command=self.handle_cancel)
        cancel_btn.pack(side=tk.RIGHT)
        
        # Bind Enter key to register
        self.root.bind('<Return>', lambda e: self.handle_register())
    
    def handle_register(self):
        """Handle registration"""
        username = self.username_var.get().strip()
        email = self.email_var.get().strip()
        password = self.password_var.get()
        confirm_password = self.confirm_password_var.get()
        
        # Validation
        if not username or not password:
            messagebox.showerror("Error", "Username and password are required")
            return
        
        if len(username) < 3:
            messagebox.showerror("Error", "Username must be at least 3 characters long")
            return
        
        if len(password) < 6:
            messagebox.showerror("Error", "Password must be at least 6 characters long")
            return
        
        if password != confirm_password:
            messagebox.showerror("Error", "Passwords do not match")
            return
        
        if ' ' in password:
            messagebox.showerror("Error", "Password cannot contain spaces")
            return
        
        # Create user
        if db_manager.create_user(username, password, email):
            messagebox.showinfo("Success", f"User '{username}' registered successfully!\nYou can now login with your credentials.")
            self.root.destroy()
        else:
            messagebox.showerror("Error", "Username already exists. Please choose a different username.")
    
    def handle_cancel(self):
        """Handle cancel button click"""
        self.root.destroy()

# Global session manager
session_manager = SessionManager()

def require_auth(func):
    """Decorator to require authentication for functions"""
    def wrapper(*args, **kwargs):
        if not session_manager.is_authenticated():
            login_window = LoginWindow(session_manager)
            if not login_window.show_login():
                return None
        return func(*args, **kwargs)
    return wrapper

def get_current_user():
    """Get current authenticated user"""
    return session_manager.get_current_user()

def logout():
    """Logout current user"""
    session_manager.logout()
