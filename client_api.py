"""
Client API module for communicating with the Flask server
Provides HTTP-based communication for remote access
"""
import requests
import json
from typing import Optional, Dict, List, Any
from logging_config import log_error, log_info, log_warning
import os
from datetime import datetime

class APIClient:
    """Client for communicating with the Flask server API"""
    
    def __init__(self, base_url: str = None, timeout: int = 30):
        self.base_url = base_url or os.environ.get('API_BASE_URL', 'http://localhost:5000')
        self.timeout = timeout
        self.session = requests.Session()
        self.authenticated = False
        
    def _make_request(self, method: str, endpoint: str, data: Dict = None, params: Dict = None) -> Dict:
        """Make HTTP request to the server"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, params=params, timeout=self.timeout)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data, timeout=self.timeout)
            elif method.upper() == 'PUT':
                response = self.session.put(url, json=data, timeout=self.timeout)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url, timeout=self.timeout)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            # Check if response is JSON
            try:
                result = response.json()
            except json.JSONDecodeError:
                result = {'success': False, 'error': 'Invalid JSON response', 'status_code': response.status_code}
            
            # Add status code to result
            result['status_code'] = response.status_code
            
            if response.status_code >= 400:
                log_warning(f"API request failed: {method} {endpoint} - Status: {response.status_code}")
            
            return result
            
        except requests.exceptions.ConnectionError:
            log_error(Exception("Connection error"), f"Failed to connect to server at {self.base_url}")
            return {'success': False, 'error': 'Connection error - server may be offline', 'offline': True}
        except requests.exceptions.Timeout:
            log_error(Exception("Timeout error"), f"Request timeout for {method} {endpoint}")
            return {'success': False, 'error': 'Request timeout', 'timeout': True}
        except Exception as e:
            log_error(e, f"API request error: {method} {endpoint}")
            return {'success': False, 'error': str(e)}
    
    def login(self, username: str, password: str) -> Dict:
        """Login to the server"""
        data = {
            'username': username,
            'password': password
        }
        
        result = self._make_request('POST', '/login', data)
        
        if result.get('success', False) or result.get('status_code') == 200:
            self.authenticated = True
            log_info(f"Successfully logged in as {username}")
        else:
            log_warning(f"Login failed for user {username}")
        
        return result
    
    def logout(self) -> Dict:
        """Logout from the server"""
        result = self._make_request('GET', '/logout')
        self.authenticated = False
        log_info("Logged out from server")
        return result
    
    def health_check(self) -> Dict:
        """Check server health"""
        return self._make_request('GET', '/health')
    
    def is_server_online(self) -> bool:
        """Check if server is online"""
        result = self.health_check()
        return result.get('success', False) and not result.get('offline', False)
    
    # Folder structure methods
    def get_folder_structures(self) -> Dict:
        """Get user's folder structures"""
        return self._make_request('GET', '/api/folder-structures')
    
    def save_folder_structure(self, client_name: str, project_name: str, structure_data: List) -> Dict:
        """Save folder structure to server"""
        data = {
            'client_name': client_name,
            'project_name': project_name,
            'structure_data': structure_data
        }
        return self._make_request('POST', '/api/folder-structures', data)
    
    # Backup task methods
    def get_backup_tasks(self) -> Dict:
        """Get user's backup tasks"""
        return self._make_request('GET', '/api/backup-tasks')
    
    def create_backup_task(self, name: str, source_path: str, destination_path: str, interval_seconds: int) -> Dict:
        """Create new backup task"""
        data = {
            'name': name,
            'source_path': source_path,
            'destination_path': destination_path,
            'interval_seconds': interval_seconds
        }
        return self._make_request('POST', '/api/backup-tasks', data)
    
    # Activity log methods
    def get_activity_logs(self) -> Dict:
        """Get activity logs"""
        return self._make_request('GET', '/api/activity-logs')

class OfflineMode:
    """Handles offline mode when server is not available"""
    
    def __init__(self):
        self.offline_data_file = 'offline_data.json'
        self.pending_operations = []
    
    def save_offline_data(self, operation_type: str, data: Dict):
        """Save operation for later sync when online"""
        operation = {
            'type': operation_type,
            'data': data,
            'timestamp': str(datetime.now())
        }
        
        self.pending_operations.append(operation)
        self._save_to_file()
        log_info(f"Saved offline operation: {operation_type}")
    
    def _save_to_file(self):
        """Save pending operations to file"""
        try:
            with open(self.offline_data_file, 'w') as f:
                json.dump(self.pending_operations, f, indent=2)
        except Exception as e:
            log_error(e, "Error saving offline data")
    
    def _load_from_file(self):
        """Load pending operations from file"""
        try:
            if os.path.exists(self.offline_data_file):
                with open(self.offline_data_file, 'r') as f:
                    self.pending_operations = json.load(f)
        except Exception as e:
            log_error(e, "Error loading offline data")
            self.pending_operations = []
    
    def sync_when_online(self, api_client: APIClient) -> Dict:
        """Sync pending operations when server comes back online"""
        if not api_client.is_server_online():
            return {'success': False, 'error': 'Server still offline'}
        
        self._load_from_file()
        
        if not self.pending_operations:
            return {'success': True, 'message': 'No pending operations to sync'}
        
        synced_count = 0
        failed_operations = []
        
        for operation in self.pending_operations:
            try:
                op_type = operation['type']
                op_data = operation['data']
                
                if op_type == 'save_folder_structure':
                    result = api_client.save_folder_structure(
                        op_data['client_name'],
                        op_data['project_name'],
                        op_data['structure_data']
                    )
                elif op_type == 'create_backup_task':
                    result = api_client.create_backup_task(
                        op_data['name'],
                        op_data['source_path'],
                        op_data['destination_path'],
                        op_data['interval_seconds']
                    )
                else:
                    log_warning(f"Unknown operation type: {op_type}")
                    continue
                
                if result.get('success', False):
                    synced_count += 1
                    log_info(f"Synced offline operation: {op_type}")
                else:
                    failed_operations.append(operation)
                    log_warning(f"Failed to sync operation: {op_type}")
                    
            except Exception as e:
                log_error(e, f"Error syncing operation: {operation}")
                failed_operations.append(operation)
        
        # Update pending operations with failed ones
        self.pending_operations = failed_operations
        self._save_to_file()
        
        return {
            'success': True,
            'synced_count': synced_count,
            'failed_count': len(failed_operations),
            'message': f"Synced {synced_count} operations, {len(failed_operations)} failed"
        }

class HybridClient:
    """Hybrid client that can work both online and offline"""
    
    def __init__(self, server_url: str = None):
        self.api_client = APIClient(server_url)
        self.offline_mode = OfflineMode()
        self._online = None  # Cache online status
    
    def is_online(self) -> bool:
        """Check if we're currently online"""
        if self._online is None:
            self._online = self.api_client.is_server_online()
        return self._online
    
    def refresh_online_status(self):
        """Refresh the online status cache"""
        self._online = self.api_client.is_server_online()
        return self._online
    
    def login(self, username: str, password: str) -> Dict:
        """Login - requires online connection"""
        if not self.is_online():
            return {'success': False, 'error': 'Cannot login while offline'}
        
        return self.api_client.login(username, password)
    
    def save_folder_structure(self, client_name: str, project_name: str, structure_data: List) -> Dict:
        """Save folder structure - works online/offline"""
        if self.is_online():
            result = self.api_client.save_folder_structure(client_name, project_name, structure_data)
            if result.get('success', False):
                return result
            # If online request fails, fall back to offline mode
        
        # Save for offline sync
        self.offline_mode.save_offline_data('save_folder_structure', {
            'client_name': client_name,
            'project_name': project_name,
            'structure_data': structure_data
        })
        
        return {
            'success': True,
            'message': 'Saved offline - will sync when server is available',
            'offline': True
        }
    
    def create_backup_task(self, name: str, source_path: str, destination_path: str, interval_seconds: int) -> Dict:
        """Create backup task - works online/offline"""
        if self.is_online():
            result = self.api_client.create_backup_task(name, source_path, destination_path, interval_seconds)
            if result.get('success', False):
                return result
        
        # Save for offline sync
        self.offline_mode.save_offline_data('create_backup_task', {
            'name': name,
            'source_path': source_path,
            'destination_path': destination_path,
            'interval_seconds': interval_seconds
        })
        
        return {
            'success': True,
            'message': 'Saved offline - will sync when server is available',
            'offline': True
        }
    
    def sync_offline_data(self) -> Dict:
        """Sync offline data when connection is restored"""
        if not self.refresh_online_status():
            return {'success': False, 'error': 'Server still offline'}
        
        return self.offline_mode.sync_when_online(self.api_client)

# Global client instance
hybrid_client = HybridClient()
