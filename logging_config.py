"""
Comprehensive logging configuration for the application
"""
import logging
import logging.handlers
import os
import sys
from datetime import datetime
from typing import Optional
from database import db_manager

class DatabaseLogHandler(logging.Handler):
    """Custom log handler that writes to database"""
    
    def __init__(self):
        super().__init__()
        self.setLevel(logging.INFO)
    
    def emit(self, record):
        """Emit a log record to database"""
        try:
            # Get current user if available
            user_id = None
            try:
                from auth import get_current_user
                current_user = get_current_user()
                if current_user:
                    user_id = current_user['id']
            except:
                pass
            
            # Format the log message
            log_message = self.format(record)
            
            # Determine action based on log level and message
            action = f"{record.levelname.lower()}"
            if hasattr(record, 'action'):
                action = record.action
            
            # Log to database
            db_manager.log_activity(
                user_id=user_id,
                action=action,
                details=log_message,
                ip_address=getattr(record, 'ip_address', None)
            )
        except Exception:
            # Don't let logging errors crash the application
            pass

class ApplicationLogger:
    """Centralized logging configuration"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = log_dir
        self.ensure_log_directory()
        self.setup_logging()
    
    def ensure_log_directory(self):
        """Create log directory if it doesn't exist"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def setup_logging(self):
        """Setup comprehensive logging configuration"""
        # Root logger configuration
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)
        
        # Clear any existing handlers
        root_logger.handlers.clear()
        
        # Console handler for development
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # File handler for all logs
        all_logs_file = os.path.join(self.log_dir, "application.log")
        file_handler = logging.handlers.RotatingFileHandler(
            all_logs_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
        
        # Error file handler
        error_file = os.path.join(self.log_dir, "errors.log")
        error_handler = logging.handlers.RotatingFileHandler(
            error_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        root_logger.addHandler(error_handler)
        
        # Database handler for important events
        db_handler = DatabaseLogHandler()
        db_formatter = logging.Formatter('%(message)s')
        db_handler.setFormatter(db_formatter)
        root_logger.addHandler(db_handler)
        
        # User activity file handler
        user_activity_file = os.path.join(self.log_dir, "user_activity.log")
        activity_handler = logging.handlers.RotatingFileHandler(
            user_activity_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3
        )
        activity_handler.setLevel(logging.INFO)
        activity_formatter = logging.Formatter(
            '%(asctime)s - USER:%(user)s - %(action)s - %(message)s'
        )
        activity_handler.setFormatter(activity_formatter)
        
        # Create user activity logger
        user_logger = logging.getLogger('user_activity')
        user_logger.addHandler(activity_handler)
        user_logger.setLevel(logging.INFO)
        user_logger.propagate = False
        
        # System events file handler
        system_events_file = os.path.join(self.log_dir, "system_events.log")
        system_handler = logging.handlers.RotatingFileHandler(
            system_events_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3
        )
        system_handler.setLevel(logging.INFO)
        system_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        system_handler.setFormatter(system_formatter)
        
        # Create system logger
        system_logger = logging.getLogger('system_events')
        system_logger.addHandler(system_handler)
        system_logger.setLevel(logging.INFO)
        system_logger.propagate = False

class UserActivityLogger:
    """Logger specifically for user activities"""
    
    def __init__(self):
        self.logger = logging.getLogger('user_activity')
    
    def log_user_action(self, action: str, details: str = None, user_id: Optional[int] = None, 
                       username: Optional[str] = None):
        """Log user action with context"""
        try:
            # Get current user if not provided
            if not user_id or not username:
                from auth import get_current_user
                current_user = get_current_user()
                if current_user:
                    user_id = current_user['id']
                    username = current_user['username']
                else:
                    username = "anonymous"
            
            # Create log record with extra fields
            extra = {
                'user': username,
                'action': action,
                'user_id': user_id
            }
            
            message = details if details else action
            self.logger.info(message, extra=extra)
            
        except Exception as e:
            # Fallback logging
            logging.error(f"Error in user activity logging: {e}")

class SystemEventLogger:
    """Logger for system events"""
    
    def __init__(self):
        self.logger = logging.getLogger('system_events')
    
    def log_startup(self):
        """Log application startup"""
        self.logger.info("Application started")
    
    def log_shutdown(self):
        """Log application shutdown"""
        self.logger.info("Application shutdown")
    
    def log_database_event(self, event: str, details: str = None):
        """Log database events"""
        message = f"DATABASE: {event}"
        if details:
            message += f" - {details}"
        self.logger.info(message)
    
    def log_backup_event(self, event: str, task_name: str, details: str = None):
        """Log backup events"""
        message = f"BACKUP: {event} - Task: {task_name}"
        if details:
            message += f" - {details}"
        self.logger.info(message)
    
    def log_folder_operation(self, operation: str, path: str, details: str = None):
        """Log folder operations"""
        message = f"FOLDER: {operation} - Path: {path}"
        if details:
            message += f" - {details}"
        self.logger.info(message)
    
    def log_network_event(self, event: str, details: str = None):
        """Log network events"""
        message = f"NETWORK: {event}"
        if details:
            message += f" - {details}"
        self.logger.info(message)
    
    def log_security_event(self, event: str, details: str = None):
        """Log security events"""
        message = f"SECURITY: {event}"
        if details:
            message += f" - {details}"
        self.logger.warning(message)

def setup_application_logging():
    """Initialize application logging"""
    app_logger = ApplicationLogger()
    return app_logger

def get_user_activity_logger():
    """Get user activity logger instance"""
    return UserActivityLogger()

def get_system_event_logger():
    """Get system event logger instance"""
    return SystemEventLogger()

# Initialize logging when module is imported
app_logger = setup_application_logging()
user_activity_logger = get_user_activity_logger()
system_event_logger = get_system_event_logger()

# Log system startup
system_event_logger.log_startup()

def log_function_call(func_name: str, args: tuple = None, kwargs: dict = None):
    """Decorator to log function calls"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = logging.getLogger(func.__module__)
            try:
                # Log function entry
                logger.debug(f"Entering {func_name}")
                
                # Execute function
                result = func(*args, **kwargs)
                
                # Log successful completion
                logger.debug(f"Completed {func_name}")
                
                return result
            except Exception as e:
                # Log error
                logger.error(f"Error in {func_name}: {str(e)}", exc_info=True)
                raise
        return wrapper
    return decorator

def log_error(error: Exception, context: str = None):
    """Log error with context"""
    logger = logging.getLogger(__name__)
    message = f"Error: {str(error)}"
    if context:
        message = f"{context} - {message}"
    logger.error(message, exc_info=True)

def log_warning(message: str, context: str = None):
    """Log warning with context"""
    logger = logging.getLogger(__name__)
    full_message = message
    if context:
        full_message = f"{context} - {message}"
    logger.warning(full_message)

def log_info(message: str, context: str = None):
    """Log info with context"""
    logger = logging.getLogger(__name__)
    full_message = message
    if context:
        full_message = f"{context} - {message}"
    logger.info(full_message)
