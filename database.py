"""
Database models and configuration for the centralized application
"""
import sqlite3
import json
import hashlib
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """Centralized database manager for the application"""
    
    def __init__(self, db_path: str = "app_database.db"):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """Get database connection with row factory"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """Initialize database with all required tables"""
        conn = self.get_connection()
        try:
            # Users table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    email TEXT,
                    role TEXT DEFAULT 'user',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            ''')
            
            # Folder structures table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS folder_structures (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    client_name TEXT NOT NULL,
                    project_name TEXT NOT NULL,
                    structure_data TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    UNIQUE(user_id, client_name, project_name)
                )
            ''')
            
            # Backup tasks table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS backup_tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    name TEXT NOT NULL,
                    source_path TEXT NOT NULL,
                    destination_path TEXT NOT NULL,
                    interval_seconds INTEGER NOT NULL,
                    status TEXT DEFAULT 'stopped',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_backup TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Activity logs table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS activity_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    action TEXT NOT NULL,
                    details TEXT,
                    ip_address TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Application settings table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS app_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT UNIQUE NOT NULL,
                    value TEXT NOT NULL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            logger.info("Database initialized successfully")
            
            # Create default admin user if no users exist
            self._create_default_admin()
            
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    def _create_default_admin(self):
        """Create default admin user if no users exist"""
        conn = self.get_connection()
        try:
            cursor = conn.execute("SELECT COUNT(*) as count FROM users")
            user_count = cursor.fetchone()['count']
            
            if user_count == 0:
                admin_password = "admin123"  # Default password - should be changed
                password_hash = self._hash_password(admin_password)
                
                conn.execute('''
                    INSERT INTO users (username, password_hash, email, role)
                    VALUES (?, ?, ?, ?)
                ''', ("admin", password_hash, "admin@localhost", "admin"))
                
                conn.commit()
                logger.info("Default admin user created (username: admin, password: admin123)")
                
        except Exception as e:
            logger.error(f"Error creating default admin: {e}")
        finally:
            conn.close()
    
    def _hash_password(self, password: str) -> str:
        """Hash password using SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password: str, password_hash: str) -> bool:
        """Verify password against hash"""
        return self._hash_password(password) == password_hash
    
    # User management methods
    def create_user(self, username: str, password: str, email: str = None, role: str = "user") -> bool:
        """Create a new user"""
        conn = self.get_connection()
        try:
            password_hash = self._hash_password(password)
            conn.execute('''
                INSERT INTO users (username, password_hash, email, role)
                VALUES (?, ?, ?, ?)
            ''', (username, password_hash, email, role))
            conn.commit()
            self.log_activity(None, "user_created", f"User {username} created")
            return True
        except sqlite3.IntegrityError:
            logger.warning(f"User {username} already exists")
            return False
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            return False
        finally:
            conn.close()
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """Authenticate user and return user data"""
        conn = self.get_connection()
        try:
            cursor = conn.execute('''
                SELECT id, username, password_hash, email, role, is_active
                FROM users WHERE username = ? AND is_active = 1
            ''', (username,))
            
            user = cursor.fetchone()
            if user and self.verify_password(password, user['password_hash']):
                # Update last login
                conn.execute('''
                    UPDATE users SET last_login = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (user['id'],))
                conn.commit()
                
                user_dict = dict(user)
                del user_dict['password_hash']  # Don't return password hash
                
                self.log_activity(user['id'], "login", f"User {username} logged in")
                return user_dict
            return None
        except Exception as e:
            logger.error(f"Error authenticating user: {e}")
            return None
        finally:
            conn.close()
    
    def get_user_by_id(self, user_id: int) -> Optional[Dict]:
        """Get user by ID"""
        conn = self.get_connection()
        try:
            cursor = conn.execute('''
                SELECT id, username, email, role, created_at, last_login, is_active
                FROM users WHERE id = ?
            ''', (user_id,))
            
            user = cursor.fetchone()
            return dict(user) if user else None
        except Exception as e:
            logger.error(f"Error getting user: {e}")
            return None
        finally:
            conn.close()
    
    # Folder structure methods
    def save_folder_structure(self, user_id: int, client_name: str, project_name: str, structure_data: List) -> bool:
        """Save folder structure to database"""
        conn = self.get_connection()
        try:
            structure_json = json.dumps(structure_data)
            
            # Try to update existing structure first
            cursor = conn.execute('''
                UPDATE folder_structures 
                SET structure_data = ?, updated_at = CURRENT_TIMESTAMP
                WHERE user_id = ? AND client_name = ? AND project_name = ?
            ''', (structure_json, user_id, client_name, project_name))
            
            if cursor.rowcount == 0:
                # Insert new structure
                conn.execute('''
                    INSERT INTO folder_structures (user_id, client_name, project_name, structure_data)
                    VALUES (?, ?, ?, ?)
                ''', (user_id, client_name, project_name, structure_json))
            
            conn.commit()
            self.log_activity(user_id, "structure_saved", f"Saved structure for {client_name}/{project_name}")
            return True
        except Exception as e:
            logger.error(f"Error saving folder structure: {e}")
            return False
        finally:
            conn.close()
    
    def get_folder_structures(self, user_id: int) -> List[Dict]:
        """Get all folder structures for a user"""
        conn = self.get_connection()
        try:
            cursor = conn.execute('''
                SELECT id, client_name, project_name, structure_data, created_at, updated_at
                FROM folder_structures WHERE user_id = ?
                ORDER BY updated_at DESC
            ''', (user_id,))
            
            structures = []
            for row in cursor.fetchall():
                structure = dict(row)
                structure['structure_data'] = json.loads(structure['structure_data'])
                structures.append(structure)
            
            return structures
        except Exception as e:
            logger.error(f"Error getting folder structures: {e}")
            return []
        finally:
            conn.close()
    
    def get_folder_structure(self, user_id: int, client_name: str, project_name: str) -> Optional[Dict]:
        """Get specific folder structure"""
        conn = self.get_connection()
        try:
            cursor = conn.execute('''
                SELECT id, client_name, project_name, structure_data, created_at, updated_at
                FROM folder_structures 
                WHERE user_id = ? AND client_name = ? AND project_name = ?
            ''', (user_id, client_name, project_name))
            
            row = cursor.fetchone()
            if row:
                structure = dict(row)
                structure['structure_data'] = json.loads(structure['structure_data'])
                return structure
            return None
        except Exception as e:
            logger.error(f"Error getting folder structure: {e}")
            return None
        finally:
            conn.close()
    
    # Backup task methods
    def save_backup_task(self, user_id: int, name: str, source_path: str, 
                        destination_path: str, interval_seconds: int) -> bool:
        """Save backup task to database"""
        conn = self.get_connection()
        try:
            conn.execute('''
                INSERT INTO backup_tasks (user_id, name, source_path, destination_path, interval_seconds)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, name, source_path, destination_path, interval_seconds))
            conn.commit()
            self.log_activity(user_id, "backup_task_created", f"Created backup task: {name}")
            return True
        except Exception as e:
            logger.error(f"Error saving backup task: {e}")
            return False
        finally:
            conn.close()
    
    def get_backup_tasks(self, user_id: int) -> List[Dict]:
        """Get all backup tasks for a user"""
        conn = self.get_connection()
        try:
            cursor = conn.execute('''
                SELECT * FROM backup_tasks WHERE user_id = ?
                ORDER BY created_at DESC
            ''', (user_id,))
            
            return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Error getting backup tasks: {e}")
            return []
        finally:
            conn.close()
    
    def update_backup_task_status(self, task_id: int, status: str) -> bool:
        """Update backup task status"""
        conn = self.get_connection()
        try:
            conn.execute('''
                UPDATE backup_tasks 
                SET status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (status, task_id))
            conn.commit()
            return True
        except Exception as e:
            logger.error(f"Error updating backup task status: {e}")
            return False
        finally:
            conn.close()
    
    def delete_backup_task(self, task_id: int, user_id: int) -> bool:
        """Delete backup task"""
        conn = self.get_connection()
        try:
            cursor = conn.execute('''
                DELETE FROM backup_tasks WHERE id = ? AND user_id = ?
            ''', (task_id, user_id))
            conn.commit()
            if cursor.rowcount > 0:
                self.log_activity(user_id, "backup_task_deleted", f"Deleted backup task ID: {task_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting backup task: {e}")
            return False
        finally:
            conn.close()
    
    # Activity logging
    def log_activity(self, user_id: Optional[int], action: str, details: str = None, ip_address: str = None):
        """Log user activity"""
        conn = self.get_connection()
        try:
            conn.execute('''
                INSERT INTO activity_logs (user_id, action, details, ip_address)
                VALUES (?, ?, ?, ?)
            ''', (user_id, action, details, ip_address))
            conn.commit()
        except Exception as e:
            logger.error(f"Error logging activity: {e}")
        finally:
            conn.close()
    
    def get_activity_logs(self, user_id: Optional[int] = None, limit: int = 100) -> List[Dict]:
        """Get activity logs"""
        conn = self.get_connection()
        try:
            if user_id:
                cursor = conn.execute('''
                    SELECT al.*, u.username 
                    FROM activity_logs al
                    LEFT JOIN users u ON al.user_id = u.id
                    WHERE al.user_id = ?
                    ORDER BY al.timestamp DESC
                    LIMIT ?
                ''', (user_id, limit))
            else:
                cursor = conn.execute('''
                    SELECT al.*, u.username 
                    FROM activity_logs al
                    LEFT JOIN users u ON al.user_id = u.id
                    ORDER BY al.timestamp DESC
                    LIMIT ?
                ''', (limit,))
            
            return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Error getting activity logs: {e}")
            return []
        finally:
            conn.close()

# Global database instance
db_manager = DatabaseManager()
