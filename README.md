# Folder Management System

A comprehensive folder structure creation, management, and backup scheduling application with centralized database access, network hosting, logging system, and user authentication.

## Features

### Core Applications
- **Folder Structure Creation**: Create new folder structures with predefined templates
- **Folder Structure Update**: Update and modify existing folder structures
- **Backup Scheduler**: Automated backup tasks with scheduling capabilities

### Enhanced Features
- **Centralized Database**: SQLite database for storing all application data
- **User Authentication**: Login/logout system with password hashing
- **Network Access**: Flask web server for remote access
- **Comprehensive Logging**: Activity logs, error logs, and system events
- **Multi-user Support**: User-specific data isolation and role-based permissions
- **Web API**: RESTful API endpoints for programmatic access

## Installation

### Prerequisites
- Python 3.8 or higher
- pip (Python package installer)

### Quick Start

1. **Clone or download the application files**

2. **Install dependencies**:
   ```bash
   pip install -r Requirment.txt
   ```

3. **Start the server**:
   ```bash
   python start_server.py
   ```

4. **Access the application**:
   - Desktop GUI: `python main.py`
   - Web Interface: Open browser to `http://localhost:5000`

### Default Credentials
- **Username**: admin
- **Password**: admin123

⚠️ **Important**: Change the default password after first login!

## Usage

### Desktop Application

1. **Launch the main application**:
   ```bash
   python main.py
   ```

2. **Login** with your credentials

3. **Choose an application**:
   - **New Folder Structure**: Create new folder structures
   - **Update Folder Structure**: Modify existing structures
   - **Backup Scheduler**: Manage automated backups

### Web Interface

1. **Start the server**:
   ```bash
   python start_server.py
   ```

2. **Open browser** to `http://localhost:5000`

3. **Login** and access features through the web dashboard

### Network Access

The application can be accessed from other devices on the network:

1. **Find your IP address**:
   ```bash
   # Linux/Mac
   ip addr show

   # Windows
   ipconfig
   ```

2. **Start server on all interfaces**:
   ```bash
   python start_server.py --host 0.0.0.0 --port 5000
   ```

3. **Access from other devices**: `http://YOUR_IP_ADDRESS:5000`

## Configuration

### Environment Variables

Create a `.env` file or set environment variables:

```bash
# Database
DATABASE_PATH=app_database.db

# Server
SERVER_HOST=0.0.0.0
SERVER_PORT=5000
SECRET_KEY=your-secret-key-here

# Logging
LOG_LEVEL=INFO
LOG_DIR=logs

# Default paths
DEFAULT_SAVE_LOCATION=/path/to/your/projects
```

### Server Configuration

```bash
# Development mode with debug
python start_server.py --debug

# Production mode on specific port
python start_server.py --host 0.0.0.0 --port 8080

# Setup only (no server start)
python start_server.py --setup-only
```

## API Documentation

### Authentication
All API endpoints require authentication. Login via web interface or use session cookies.

### Endpoints

#### Folder Structures
- **GET** `/api/folder-structures` - Get user's folder structures
- **POST** `/api/folder-structures` - Create new folder structure

```json
POST /api/folder-structures
{
    "client_name": "Client Name",
    "project_name": "Project Name",
    "structure_data": [
        {"name": "folder1", "children": []},
        {"name": "folder2", "children": [
            {"name": "subfolder", "children": []}
        ]}
    ]
}
```

#### Backup Tasks
- **GET** `/api/backup-tasks` - Get user's backup tasks
- **POST** `/api/backup-tasks` - Create new backup task

```json
POST /api/backup-tasks
{
    "name": "My Backup",
    "source_path": "/path/to/source",
    "destination_path": "/path/to/destination",
    "interval_seconds": 3600
}
```

#### Activity Logs
- **GET** `/api/activity-logs` - Get activity logs (user's own or all if admin)

#### Health Check
- **GET** `/health` - Server health status
