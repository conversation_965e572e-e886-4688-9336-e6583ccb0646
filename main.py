import tkinter as tk
import subprocess
import os
import sys

from tkinter import ttk, filedialog, messagebox, simpledialog
import json
from PIL import Image, ImageTk
import re

# Import database and authentication modules
from database import db_manager
from auth import require_auth, get_current_user, session_manager, LoginWindow
from logging_config import user_activity_logger, system_event_logger, log_error, log_info
# Check authentication before starting the application
def check_authentication():
    """Check if user is authenticated, show login if not"""
    if not session_manager.is_authenticated():
        login_window = LoginWindow(session_manager)
        if not login_window.show_login():
            sys.exit(0)  # Exit if login cancelled

    # Log application start
    current_user = get_current_user()
    user_activity_logger.log_user_action("main_app_started",
                                        f"User opened main application launcher")
    system_event_logger.log_startup()

# Functions to launch external Python files
def open_app_one():
    user_activity_logger.log_user_action("launched_folder_creation", "User launched folder structure creation app")
    subprocess.Popen(["python", "folder_structure_creation.py"])

def open_app_two():
    user_activity_logger.log_user_action("launched_folder_updation", "User launched folder structure updation app")
    subprocess.Popen(["python", "folder_structure_updation.py"])

def open_app_three():
    user_activity_logger.log_user_action("launched_backup_scheduler", "User launched backup scheduler app")
    subprocess.Popen(["python", "backuptool.py"])

def show_user_management():
    """Show user management window (admin only)"""
    current_user = get_current_user()
    if current_user['role'] != 'admin':
        messagebox.showerror("Access Denied", "Only administrators can access user management")
        return

    user_activity_logger.log_user_action("accessed_user_management", "Admin accessed user management")

    # Create user management window
    user_mgmt_window = tk.Toplevel(root)
    user_mgmt_window.title("User Management")
    user_mgmt_window.geometry("800x600")

    # Add user management functionality here
    ttk.Label(user_mgmt_window, text="User Management", font=("Arial", 16, "bold")).pack(pady=10)
    ttk.Label(user_mgmt_window, text="Feature coming soon...").pack(pady=20)

def show_activity_logs():
    """Show activity logs window"""
    current_user = get_current_user()

    user_activity_logger.log_user_action("viewed_activity_logs", "User viewed activity logs")

    # Create activity logs window
    logs_window = tk.Toplevel(root)
    logs_window.title("Activity Logs")
    logs_window.geometry("1000x600")

    # Create treeview for logs
    tree = ttk.Treeview(logs_window, columns=("Time", "User", "Action", "Details"), show='headings')
    tree.heading("Time", text="Timestamp")
    tree.heading("User", text="User")
    tree.heading("Action", text="Action")
    tree.heading("Details", text="Details")

    # Get logs (admin sees all, users see only their own)
    if current_user['role'] == 'admin':
        logs = db_manager.get_activity_logs(limit=200)
    else:
        logs = db_manager.get_activity_logs(current_user['id'], limit=100)

    for log in logs:
        tree.insert('', tk.END, values=(
            log['timestamp'][:19],  # Remove microseconds
            log['username'] or 'System',
            log['action'],
            log['details'] or ''
        ))

    tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    ttk.Button(logs_window, text="Close", command=logs_window.destroy).pack(pady=10)

# Check authentication before creating UI
check_authentication()

# Main application window
root = tk.Tk()
root.title("Folder Management System - App Launcher")
root.geometry("600x400")

# --- Helper to handle image paths in PyInstaller bundle ---
def resource_path(relative_path):   
    try:
        # PyInstaller stores files in a temp folder in _MEIPASS
        base_path = sys._MEIPASS
    except AttributeError:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)


logo_path = resource_path("green.jpeg")
logo_img = Image.open(logo_path)
logo_img = logo_img.resize((300, 100))
logo_photo = ImageTk.PhotoImage(logo_img)
logo_label = ttk.Label(root, image=logo_photo)
logo_label.image = logo_photo  # Prevent garbage collection
logo_label.pack(pady=10)

# Add user info and logout section
current_user = get_current_user()
user_frame = ttk.Frame(root)
user_frame.pack(fill=tk.X, padx=20, pady=5)

user_info_label = ttk.Label(user_frame, text=f"Welcome, {current_user['username']} ({current_user['role']})")
user_info_label.pack(side=tk.LEFT)

def logout_and_exit():
    """Logout user and exit application"""
    user_activity_logger.log_user_action("logout", "User logged out from main application")
    session_manager.logout()
    root.destroy()

logout_btn = ttk.Button(user_frame, text="Logout", command=logout_and_exit)
logout_btn.pack(side=tk.RIGHT)

# App title label
title_label = tk.Label(root, text="Folder Management System", font=("Helvetica", 16, "bold"))
title_label.pack(pady=10)

subtitle_label = tk.Label(root, text="Select an Application", font=("Helvetica", 12))
subtitle_label.pack(pady=5)

# Main application buttons
app_frame = ttk.LabelFrame(root, text="Applications", padding=20)
app_frame.pack(pady=20, padx=20, fill=tk.X)

btn1 = tk.Button(app_frame, text="New Folder Structure", width=25, height=2, command=open_app_one)
btn1.pack(pady=5)

btn2 = tk.Button(app_frame, text="Update Folder Structure", width=25, height=2, command=open_app_two)
btn2.pack(pady=5)

btn3 = tk.Button(app_frame, text="Backup Scheduler", width=25, height=2, command=open_app_three)
btn3.pack(pady=5)

# Management buttons (admin features)
mgmt_frame = ttk.LabelFrame(root, text="Management", padding=20)
mgmt_frame.pack(pady=10, padx=20, fill=tk.X)

if current_user['role'] == 'admin':
    user_mgmt_btn = tk.Button(mgmt_frame, text="User Management", width=25, command=show_user_management)
    user_mgmt_btn.pack(side=tk.LEFT, padx=5)

logs_btn = tk.Button(mgmt_frame, text="Activity Logs", width=25, command=show_activity_logs)
logs_btn.pack(side=tk.LEFT, padx=5)

# Run the app
root.mainloop()
